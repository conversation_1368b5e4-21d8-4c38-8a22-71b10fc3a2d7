/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-title {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.controls-info {
    margin-top: 10px;
    font-size: 0.9rem;
    opacity: 0.8;
    color: #cccccc;
}

/* Game Layout */
.game-layout {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 30px;
    flex: 1;
}

/* Panels */
.left-panel, .right-panel {
    width: 200px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Game Board Container */
.game-board-container {
    position: relative;
    border: 3px solid #00ffff;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.8);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
    overflow: hidden;
}

#gameCanvas {
    display: block;
    background: #000000;
}

/* Game Overlay */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.game-overlay.active {
    display: flex;
}

.overlay-content {
    text-align: center;
    padding: 40px;
    border: 2px solid #00ffff;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.9);
}

#overlayTitle {
    font-size: 2rem;
    margin-bottom: 20px;
    color: #00ffff;
}

#overlayMessage {
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* UI Containers */
.hold-container, .next-container, .score-container, .controls-container {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #333;
    border-radius: 10px;
    padding: 15px;
}

.hold-container h3, .next-container h3 {
    text-align: center;
    margin-bottom: 10px;
    color: #00ffff;
    font-size: 1rem;
}

#holdCanvas, #nextCanvas {
    display: block;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #444;
    border-radius: 5px;
}

/* Score Display */
.score-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #333;
}

.score-label {
    font-size: 0.8rem;
    color: #cccccc;
}

.score-value {
    font-weight: 700;
    color: #00ffff;
}

/* Controls */
.audio-controls h4 {
    margin-bottom: 15px;
    color: #00ffff;
    text-align: center;
}

.volume-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.volume-control label {
    font-size: 0.8rem;
    color: #cccccc;
}

.volume-control input[type="range"] {
    width: 100px;
    height: 5px;
    background: #333;
    outline: none;
    border-radius: 5px;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 15px;
    height: 15px;
    background: #00ffff;
    border-radius: 50%;
    cursor: pointer;
}

/* Buttons */
.game-button {
    background: linear-gradient(45deg, #0066cc, #00ffff);
    border: none;
    color: white;
    padding: 12px 20px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px 0;
    text-transform: uppercase;
}

.game-button:hover {
    background: linear-gradient(45deg, #0088ff, #00ffff);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    transform: translateY(-2px);
}

.game-button:active {
    transform: translateY(0);
}

/* Mobile Controls */
.mobile-controls {
    display: none;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #00ffff;
    border-radius: 10px;
    padding: 15px;
    z-index: 100;
}

.control-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.control-row:last-child {
    margin-bottom: 0;
}

.touch-btn {
    background: rgba(0, 102, 204, 0.8);
    border: 1px solid #00ffff;
    color: white;
    padding: 15px 20px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
    font-size: 0.9rem;
}

.touch-btn:active {
    background: rgba(0, 255, 255, 0.8);
    transform: scale(0.95);
}

/* Particle Container */
#particleContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* Animations */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes lineClearing {
    0% { opacity: 1; transform: scaleY(1); }
    50% { opacity: 0.5; transform: scaleY(0.8); }
    100% { opacity: 0; transform: scaleY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-layout {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }
    
    .left-panel, .right-panel {
        width: 100%;
        max-width: 300px;
        flex-direction: row;
        justify-content: space-around;
    }
    
    .mobile-controls {
        display: block;
    }
    
    .controls-info {
        display: none;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    #gameCanvas {
        width: 300px;
        height: 600px;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 10px;
    }
    
    #gameCanvas {
        width: 250px;
        height: 500px;
    }
    
    .left-panel, .right-panel {
        flex-direction: column;
    }
}
